<script setup lang="ts">
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { computed } from 'vue'

interface PaginationProps {
  currentPage: number
  pageSize: number
  total: number
  totalPages: number
  hasNextPage: boolean
  hasPrevPage: boolean
  isLoading?: boolean
}

const props = withDefaults(defineProps<PaginationProps>(), {
  isLoading: false
})

const emit = defineEmits<{
  (e: 'goToPage', page: number): void
  (e: 'nextPage'): void
  (e: 'prevPage'): void
  (e: 'changePageSize', size: number): void
}>()

// 计算显示的页码范围
const pageRange = computed(() => {
  const range = []
  const maxVisible = 5
  let start = Math.max(1, props.currentPage - Math.floor(maxVisible / 2))
  let end = Math.min(props.totalPages, start + maxVisible - 1)
  
  // 调整起始位置
  if (end - start + 1 < maxVisible) {
    start = Math.max(1, end - maxVisible + 1)
  }
  
  for (let i = start; i <= end; i++) {
    range.push(i)
  }
  
  return range
})

// 页大小选项
const pageSizeOptions = [10, 20, 50, 100]

// 计算显示的数据范围
const dataRange = computed(() => {
  const start = (props.currentPage - 1) * props.pageSize + 1
  const end = Math.min(props.currentPage * props.pageSize, props.total)
  return { start, end }
})

function handlePageSizeChange(value: string) {
  emit('changePageSize', parseInt(value))
}
</script>

<template>
  <div class="flex items-center justify-between px-4 py-3 border-t bg-background">
    <!-- 数据统计信息 -->
    <div class="flex items-center text-sm text-muted-foreground">
      <span v-if="total > 0">
        显示第 {{ dataRange.start }} - {{ dataRange.end }} 条，共 {{ total }} 条记录
      </span>
      <span v-else>
        暂无数据
      </span>
    </div>

    <!-- 分页控件 -->
    <div class="flex items-center space-x-2">
      <!-- 页大小选择器 -->
      <div class="flex items-center space-x-2">
        <span class="text-sm text-muted-foreground">每页</span>
        <Select :value="pageSize.toString()" @update:value="handlePageSizeChange">
          <SelectTrigger class="w-16 h-8">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem 
              v-for="size in pageSizeOptions" 
              :key="size" 
              :value="size.toString()"
            >
              {{ size }}
            </SelectItem>
          </SelectContent>
        </Select>
        <span class="text-sm text-muted-foreground">条</span>
      </div>

      <!-- 分页按钮 -->
      <div class="flex items-center space-x-1">
        <!-- 上一页 -->
        <Button
          variant="outline"
          size="sm"
          :disabled="!hasPrevPage || isLoading"
          @click="emit('prevPage')"
        >
          上一页
        </Button>

        <!-- 页码按钮 -->
        <template v-if="totalPages > 1">
          <!-- 第一页 -->
          <Button
            v-if="pageRange[0] > 1"
            variant="outline"
            size="sm"
            :disabled="isLoading"
            @click="emit('goToPage', 1)"
          >
            1
          </Button>
          
          <!-- 省略号 -->
          <span v-if="pageRange[0] > 2" class="px-2 text-muted-foreground">...</span>

          <!-- 页码范围 -->
          <Button
            v-for="page in pageRange"
            :key="page"
            :variant="page === currentPage ? 'default' : 'outline'"
            size="sm"
            :disabled="isLoading"
            @click="emit('goToPage', page)"
          >
            {{ page }}
          </Button>

          <!-- 省略号 -->
          <span v-if="pageRange[pageRange.length - 1] < totalPages - 1" class="px-2 text-muted-foreground">...</span>

          <!-- 最后一页 -->
          <Button
            v-if="pageRange[pageRange.length - 1] < totalPages"
            variant="outline"
            size="sm"
            :disabled="isLoading"
            @click="emit('goToPage', totalPages)"
          >
            {{ totalPages }}
          </Button>
        </template>

        <!-- 下一页 -->
        <Button
          variant="outline"
          size="sm"
          :disabled="!hasNextPage || isLoading"
          @click="emit('nextPage')"
        >
          下一页
        </Button>
      </div>
    </div>
  </div>
</template>
