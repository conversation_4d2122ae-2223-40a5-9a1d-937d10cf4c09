<script lang="ts" setup>
import type { Ticket } from './data/tickets'

import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from '@/components/ui/resizable'

import { Separator } from '@/components/ui/separator'
import { TooltipProvider } from '@/components/ui/tooltip'
import { useDebounceFn } from '@vueuse/core'
import { computed, nextTick, onMounted, ref, watch } from 'vue'

import TicketFilterToolbar from './components/TicketFilterToolbar.vue'
import TicketPagination from './components/TicketPagination.vue'
import TicketDisplay from './Display.vue'
import TicketList from './List.vue'

interface TicketProps {
  tickets: Ticket[]
  defaultLayout?: number[]
  defaultCollapsed?: boolean
  navCollapsedSize: number
  activeMenu: string
  selectedTicketId?: string
  // 分页相关props
  isLoading?: boolean
  currentPage?: number
  pageSize?: number
  total?: number
  totalPages?: number
  hasNextPage?: boolean
  hasPrevPage?: boolean
}

// 不再需要LinkProp接口

const props = withDefaults(defineProps<TicketProps>(), {
  defaultCollapsed: false,
  defaultLayout: () => [26, 30, 70],
  selectedTicketId: '',
  isLoading: false,
  currentPage: 1,
  pageSize: 10,
  total: 0,
  totalPages: 0,
  hasNextPage: false,
  hasPrevPage: false,
})

// 定义 emit 事件
const emit = defineEmits<{
  (e: 'update:activeMenu', menu: string): void
  (e: 'goToPage', page: number): void
  (e: 'nextPage'): void
  (e: 'prevPage'): void
  (e: 'changePageSize', size: number): void
  (e: 'search', keyword: string): void
  (e: 'filter', filters: Record<string, string[]>): void
}>()

// 使用 computed 来确保 tickets 响应 props.tickets 的变化
const tickets = computed(() => props.tickets)
// 不再需要router

// 不再需要isCollapsed变量
const selectedTicket = ref<string | undefined>()
// 初始化选中的工单，优先使用传入的工单ID
if (props.selectedTicketId) {
  selectedTicket.value = props.selectedTicketId
}
else if (props.tickets && props.tickets.length > 0 && props.tickets[0]) {
  selectedTicket.value = props.tickets[0].id
}
const searchValue = ref('')
const isMobile = ref(false)

// 筛选条件
const activeFilters = ref<Record<string, string[]>>({})

onMounted(() => {
  // 设置移动设备检测
  isMobile.value = window.innerWidth < 768
  window.addEventListener('resize', () => {
    isMobile.value = window.innerWidth < 768
  })

  // 确保selectedMenu与props.activeMenu同步
  if (props.activeMenu) {
    selectedMenu.value = props.activeMenu
  }

  // 组件挂载后，如果有指定的工单ID，确保它被正确选中
  if (props.selectedTicketId && tickets.value && tickets.value.length > 0) {
    const ticketExists = tickets.value.some(
      item =>
        item.id === props.selectedTicketId
        || item.id.toString() === props.selectedTicketId
        || props.selectedTicketId === item.id.toString(),
    )
    if (ticketExists) {
      selectedTicket.value = props.selectedTicketId
      nextTick(() => {
        ensureTicketVisible(props.selectedTicketId!)
      })
    }
  }
})

// 监听activeMenu属性变化，更新selectedMenu
watch(
  () => props.activeMenu,
  (newValue) => {
    if (newValue) {
      selectedMenu.value = newValue
    }
  },
)

// 监听selectedTicketId属性变化，更新selectedTicket
watch(
  () => props.selectedTicketId,
  (newValue) => {
    if (newValue && tickets.value && tickets.value.length > 0) {
      // 检查该ID的工单是否存在（支持字符串和数字类型匹配）
      const ticketExists = tickets.value.some(
        item =>
          item.id === newValue
          || item.id.toString() === newValue
          || newValue === item.id.toString(),
      )
      if (ticketExists) {
        selectedTicket.value = newValue

        // 使用 nextTick 确保所有计算属性都已更新
        nextTick(() => {
          ensureTicketVisible(newValue)
        })
      }
    }
  },
  { immediate: true },
)

// 处理筛选事件
function handleFilter(filters: Record<string, string[]>) {
  activeFilters.value = filters
  emit('filter', filters)
}

// 处理搜索事件
function handleSearch(keyword: string) {
  emit('search', keyword)
}

// 监听搜索值变化，使用防抖
const debouncedSearchFn = useDebounceFn((keyword: string) => {
  handleSearch(keyword)
}, 300)

watch(searchValue, (newValue) => {
  debouncedSearchFn(newValue)
})

// 是否有活跃的筛选条件
const hasActiveFilters = computed(() => {
  return Object.values(activeFilters.value).some(
    arr => Array.isArray(arr) && arr.length > 0,
  )
})

// 应用筛选条件的工单列表
const filteredByFilters = computed(() => {
  // 如果没有筛选条件，返回所有工单
  if (!hasActiveFilters.value) {
    return tickets.value
  }

  // 应用筛选条件
  return tickets.value.filter((item) => {
    // 检查每个筛选类别
    for (const [key, values] of Object.entries(activeFilters.value)) {
      // 如果该类别没有选择任何值，跳过
      if (!Array.isArray(values) || values.length === 0)
        continue

      // 根据不同的字段类型进行筛选
      if (key === 'labels' || key === 'apps') {
        // 数组字段：检查是否有交集
        const itemValues = (item[key as keyof typeof item] as string[]) || []
        if (!values.some(v => itemValues.includes(v))) {
          return false
        }
      }
      else {
        // 字符串字段：检查是否匹配
        const itemValue = item[key as keyof typeof item] as string
        if (!itemValue || !values.includes(itemValue)) {
          return false
        }
      }
    }

    // 所有条件都满足
    return true
  })
})

// 应用搜索的工单列表
const filteredTicketList = computed(() => {
  let output: Ticket[] = filteredByFilters.value
  const searchTerm = searchValue.value?.trim()

  if (searchTerm) {
    output = output.filter((item) => {
      return (
        item.id.toLowerCase().includes(searchTerm.toLowerCase())
        || (item.ticketID || '')
          .toLowerCase()
          .includes(searchTerm.toLowerCase())
          || (item.title || '').toLowerCase().includes(searchTerm.toLowerCase())
          || (item.text || '').toLowerCase().includes(searchTerm.toLowerCase())
          || (item.problemDescription || '')
            .toLowerCase()
            .includes(searchTerm.toLowerCase())
            || (item.date || '')
              .toString()
              .toLowerCase()
              .includes(searchTerm.toLowerCase())
              || (item.status || item.stage || '')
                .toLowerCase()
                .includes(searchTerm.toLowerCase())
                || (item.labels || item.functionType || []).some(label =>
                  label.toLowerCase().includes(searchTerm.toLowerCase()),
                )
                || (item.creator || '')
                  .toLowerCase()
                  .includes(searchTerm.toLowerCase())
                  || (item.handler || item.devProcessor || '')
                    .toLowerCase()
                    .includes(searchTerm.toLowerCase())
      )
    })
  }

  return output
})

// 使用props.activeMenu作为初始值
const selectedMenu = ref(props.activeMenu || 'all')
const ticketLists = {
  // 所有工单
  all: computed(() => filteredTicketList.value),
  // 我创建的
  created: computed(() =>
    filteredTicketList.value.filter(
      item => item.creator?.toLowerCase() === 'admin',
    ),
  ),
  // 我待办的
  todo: computed(() =>
    filteredTicketList.value.filter(
      item => item.handler?.toLowerCase() === 'admin',
    ),
  ),
  // 待处理
  pending: computed(() =>
    filteredTicketList.value.filter(
      item => (item.stage || item.status) === '待处理',
    ),
  ),
  // 处理中
  processing: computed(() =>
    filteredTicketList.value.filter(
      item => (item.stage || item.status) === '处理中',
    ),
  ),
  // 已处理
  done: computed(() =>
    filteredTicketList.value.filter(
      item => (item.stage || item.status) === '已处理',
    ),
  ),
  // 已归档
  archived: computed(() =>
    filteredTicketList.value.filter(
      item => (item.stage || item.status) === '已归档',
    ),
  ),
}

const selectedTicketData = computed(() => {
  // 确保有选中的工单ID和工单数据
  if (!selectedTicket.value || !tickets.value || tickets.value.length === 0) {
    return undefined
  }

  // 尝试精确匹配和字符串匹配，优先使用ticketID
  const ticket = tickets.value.find(
    item =>
      item
      && (item.ticketID || item.id) // 优先使用ticketID进行匹配
      && (item.ticketID === selectedTicket.value
        || item.ticketID?.toString() === selectedTicket.value
        || selectedTicket.value === item.ticketID?.toString()
        // 兼容性：使用id进行匹配
        || item.id === selectedTicket.value
        || item.id.toString() === selectedTicket.value
        || selectedTicket.value === item.id.toString()),
  )
  return ticket
})
const currentTicketList = computed(() => {
  const menu = selectedMenu.value as keyof typeof ticketLists
  return ticketLists[menu]?.value || ticketLists.all.value
})

// 监听当前列表变化，更新选中的工单
watch(
  currentTicketList,
  (newList) => {
    try {
      // 只有在没有通过 selectedTicketId prop 指定工单的情况下，才自动选择第一个工单
      const hasSpecifiedTicket = props.selectedTicketId

      if (
        !hasSpecifiedTicket
        && newList
        && Array.isArray(newList)
        && newList.length > 0
        && (!selectedTicket.value
          || !newList.some(
            item =>
              item
              && (item.id === selectedTicket.value
                || item.id.toString() === selectedTicket.value
                || selectedTicket.value === item.id.toString()),
          )) // 确保newList[0]存在且有id属性
          && newList[0]
          && newList[0].id
      ) {
        selectedTicket.value = newList[0].id
      }
    }
    catch (error) {
      console.error('Error in currentTicketList watcher:', error)
    }
  },
  { immediate: true },
)

// 确保指定的工单在当前列表中可见
function ensureTicketVisible(ticketId: string) {
  try {
    // 检查工单是否存在
    const ticket = tickets.value.find(
      item =>
        item
        && (item.id === ticketId
          || item.id.toString() === ticketId
          || ticketId === item.id.toString()),
    )

    if (ticket) {
      // 直接访问工单详情页时，统一切换到"所有工单"菜单
      selectedMenu.value = 'all'

      // 通知父组件菜单变化
      emit('update:activeMenu', 'all')
    }
  }
  catch (error) {
    console.error('Error in ensureTicketVisible:', error)
  }
}
</script>

<template>
  <div>
    <TooltipProvider :delay-duration="0">
      <!-- 筛选工具栏 - 集成在内容区域 -->
      <div class="border-b">
        <TicketFilterToolbar
          v-model:search-value="searchValue"
          :tickets="currentTicketList"
          @filter="handleFilter"
        />
      </div>
      <ResizablePanelGroup
        id="resize-panel-group-1"
        direction="horizontal"
        class="items-stretch"
      >
        <!-- <ResizablePanel
          id="resize-panel-1"
          :default-size="defaultLayout[0]"
          :collapsed-size="navCollapsedSize"
          collapsible
          :min-size="15"
          :max-size="20"
          :class="
            cn(
              isCollapsed &&
                'min-w-[50px] transition-all duration-300 ease-in-out',
            )
          "
          @expand="onExpand"
          @collapse="onCollapse"
        >
          <div :class="cn('flex h-[52px] items-center justify-center px-2')">
            <Button variant="outline" class="w-full gap-1">
              <Icon name="i-lucide-plus" class="size-4" />
              创建工单
            </Button>
          </div>
          <Separator />
          <Nav
            :is-collapsed="isCollapsed"
            :links="links"
            @menu-click="handleMenuClick"
          />
        </ResizablePanel>
        <ResizableHandle id="resize-handle-1" with-handle />  -->
        <ResizablePanel
          id="resize-panel-2"
          :default-size="defaultLayout[1]"
          :min-size="25"
        >
          <div
            class="h-full flex flex-col bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60"
          >
            <div class="m-0 flex-1">
              <TicketList
                v-model:selected-ticket="selectedTicket"
                :items="currentTicketList"
                :search-value="searchValue"
              />
            </div>
            <!-- 分页组件 -->
            <TicketPagination
              v-if="total > 0"
              :current-page="currentPage"
              :page-size="pageSize"
              :total="total"
              :total-pages="totalPages"
              :has-next-page="hasNextPage"
              :has-prev-page="hasPrevPage"
              :is-loading="isLoading"
              @go-to-page="emit('goToPage', $event)"
              @next-page="emit('nextPage')"
              @prev-page="emit('prevPage')"
              @change-page-size="emit('changePageSize', $event)"
            />
          </div>
        </ResizablePanel>
        <ResizableHandle v-if="!isMobile" id="resize-handle-2" with-handle />
        <ResizablePanel
          v-if="!isMobile"
          id="resize-panel-3"
          :default-size="defaultLayout[2]"
          :min-size="40"
        >
          <TicketDisplay
            v-if="!isMobile || selectedTicket"
            :ticket="selectedTicketData"
          />
        </ResizablePanel>
      </ResizablePanelGroup>
    </TooltipProvider>
  </div>
</template>
