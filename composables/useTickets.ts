import type { TicketListParams, TicketListResponse } from '../api/tickets'
import type { Ticket } from '../components/list/data/tickets'
import { computed, ref } from 'vue'
import { fetchTickets as apiFetchTickets } from '../api/tickets'

export function useTickets() {
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const tickets = ref<Ticket[]>([])

  // 分页相关状态
  const currentPage = ref(1)
  const pageSize = ref(10)
  const total = ref(0)

  // 查询参数
  const searchParams = ref<TicketListParams>({})

  // 计算属性
  const totalPages = computed(() => Math.ceil(total.value / pageSize.value))
  const hasNextPage = computed(() => currentPage.value < totalPages.value)
  const hasPrevPage = computed(() => currentPage.value > 1)

  async function fetchTickets(params: TicketListParams = {}) {
    isLoading.value = true
    error.value = null

    try {
      // 合并参数
      const requestParams: TicketListParams = {
        page: currentPage.value,
        pageSize: pageSize.value,
        ...searchParams.value,
        ...params,
      }

      console.log('正在获取工单列表，参数:', requestParams)

      const response: TicketListResponse = await apiFetchTickets(requestParams)

      console.log('API响应:', response)

      // 处理响应数据
      if (response && typeof response === 'object') {
        if ('code' in response && 'data' in response) {
          // 标准API响应格式
          if (response.code === 200 || response.code === 0) {
            const data = response.data

            if (data && typeof data === 'object' && 'list' in data) {
              // 分页数据格式
              tickets.value = data.list || []
              total.value = data.total || 0
              currentPage.value = data.page || currentPage.value
              pageSize.value = data.pageSize || data.limit || pageSize.value
            }
            else if (Array.isArray(data)) {
              // 直接数组格式
              tickets.value = data
              total.value = data.length
            }
            else {
              console.warn('API返回数据格式不正确:', data)
              tickets.value = []
              total.value = 0
            }
          }
          else {
            // API返回错误
            const errorMsg = response.msg || response.message || '获取工单列表失败'
            console.warn('API返回错误:', errorMsg)
            error.value = errorMsg
            tickets.value = []
            total.value = 0
          }
        }
        else if (Array.isArray(response)) {
          // 直接返回数组（兼容旧格式）
          tickets.value = response
          total.value = response.length
        }
        else {
          console.warn('未知的响应格式:', response)
          tickets.value = []
          total.value = 0
        }
      }
      else {
        console.warn('API响应为空或格式错误')
        tickets.value = []
        total.value = 0
      }

      return tickets.value
    }
    catch (err) {
      console.error('获取工单列表失败:', err)
      error.value = err instanceof Error ? err.message : '获取工单列表失败'
      tickets.value = []
      total.value = 0
      return []
    }
    finally {
      isLoading.value = false
    }
  }

  // 刷新当前页数据
  async function refreshTickets() {
    await fetchTickets()
  }

  // 搜索工单
  async function searchTickets(keyword: string) {
    searchParams.value = {
      ...searchParams.value,
      query: {
        ...searchParams.value.query,
        keyword,
      },
    }
    currentPage.value = 1 // 搜索时重置到第一页
    await fetchTickets()
  }

  // 根据条件筛选工单
  async function filterTickets(filters: Partial<TicketListParams>) {
    searchParams.value = {
      ...searchParams.value,
      ...filters,
    }
    currentPage.value = 1 // 筛选时重置到第一页
    await fetchTickets()
  }

  // 分页相关方法
  async function goToPage(page: number) {
    if (page >= 1 && page <= totalPages.value) {
      currentPage.value = page
      await fetchTickets()
    }
  }

  async function nextPage() {
    if (hasNextPage.value) {
      await goToPage(currentPage.value + 1)
    }
  }

  async function prevPage() {
    if (hasPrevPage.value) {
      await goToPage(currentPage.value - 1)
    }
  }

  async function changePageSize(size: number) {
    pageSize.value = size
    currentPage.value = 1 // 改变页大小时重置到第一页
    await fetchTickets()
  }

  // 重置所有参数
  function resetParams() {
    searchParams.value = {}
    currentPage.value = 1
    pageSize.value = 10
  }

  return {
    // 状态
    isLoading,
    error,
    tickets,

    // 分页状态
    currentPage,
    pageSize,
    total,
    totalPages,
    hasNextPage,
    hasPrevPage,

    // 查询参数
    searchParams,

    // 方法
    fetchTickets,
    refreshTickets,
    searchTickets,
    filterTickets,

    // 分页方法
    goToPage,
    nextPage,
    prevPage,
    changePageSize,
    resetParams,
  }
}
