#app {
  width: 100%;
  height: 100%;
}

// =================================
// ==============scrollbar==========
// =================================

::-webkit-scrollbar {
  width: 7px;
  height: 8px;
}

// ::-webkit-scrollbar-track {
//   background: transparent;
// }

::-webkit-scrollbar-track {
  background-color: rgb(0 0 0 / 5%);
}

::-webkit-scrollbar-thumb {
  // background-color: rgba(144, 147, 153, 0.3);
  border-radius: 2px;
  // background: rgba(0, 0, 0, 0.6);
  background-color: rgb(144 147 153 / 30%);
  box-shadow: inset 0 0 6px rgb(0 0 0 / 20%);
}

::-webkit-scrollbar-thumb:hover {
  background-color: @border-color-dark;
}

::-webkit-scrollbar-corner {
  background-color: transparent;
}

// =================================
// ==============nprogress==========
// =================================
#nprogress {
  pointer-events: none;

  .bar {
    position: fixed;
    z-index: 99999;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    opacity: 0.75;
    background-color: @primary-color;
  }
}
