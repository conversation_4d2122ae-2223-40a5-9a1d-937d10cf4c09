@import (reference) '../color.less';

// disabled自定义样式

// input
.ant-input {
  &[disabled],
  &[disabled]:hover {
    //  color: fade(@button-cancel-color, 40%) !important;
    //  background: fade(@button-cancel-bg-color, 40%) !important;
    //  border-color: fade(@button-cancel-border-color, 40%) !important;
    border-color: @white !important;
    background: @white !important;
    color: @text-color !important;
    cursor: text !important;
  }
}

.ant-picker-disabled {
  // border-radius: 0 !important;
  border-color: @white !important;
  background: @white !important;
  cursor: text !important;

  .ant-picker-input {
    input {
      border-radius: 0 !important;
      color: @text-color !important;
      cursor: text !important;
    }

    .ant-picker-suffix {
      display: none;
    }
  }
}

.ant-input-affix-wrapper-disabled {
  border-color: @white !important;
  background: @white !important;
  cursor: text !important;
}

.ant-select-disabled {
  .ant-select-selector {
    // padding: 0 !important;
    border-color: @white !important;
    background: @white !important;
    color: @text-color !important;
    cursor: text !important;
  }

  .ant-select-arrow {
    display: none;
  }
}
